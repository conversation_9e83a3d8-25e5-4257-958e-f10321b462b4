apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: rd1
resources:
- ../../base
patches:
- path: ./rollout.patch.yaml
- path: ./rollout.internal.patch.yaml
- path: ./rollout.consumer.patch.yaml
- path: ./service.patch.yaml
- path: ./service.internal.patch.yaml
- path: ./service.consumer.patch.yaml
- path: ./ingress.patch.yaml
- path: ./ingress.internal.patch.yaml
- path: ./servicemonitor.patch.yml
- path: ./servicemonitor.internal.patch.yml
- path: ./servicemonitor.consumer.patch.yml
- path: ./prometheus-rules.patch.yml
- path: ./prometheus-rules.internal.patch.yml
- path: ./prometheus-rules.consumer.patch.yml
- path: ./prometheus-rules.success-rate.patch.yml
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/prod/onlinepay-consumer-idc
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/prod/onlinepay-idc
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/prod/onlinepay-internal-idc
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
