apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: rd1
namePrefix: sit-
resources:
- ../../base
patches:
- path: ./rollout.patch.yaml
- path: ./rollout.internal.patch.yaml
- path: ./rollout.consumer.patch.yaml
- path: ./service.patch.yaml
- path: ./service.internal.patch.yaml
- path: ./service.consumer.patch.yaml
- path: ./ingress.patch.yaml
- path: ./ingress.internal.patch.yaml
- path: ./servicemonitor.patch.yml
- path: ./servicemonitor.internal.patch.yml
- path: ./servicemonitor.consumer.patch.yml
- path: ./prometheus-rules.patch.yml
- path: ./prometheus-rules.internal.patch.yml
- path: ./prometheus-rules.consumer.patch.yml
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/sit/onlinepay-consumer-idc
  newTag: e8ebf4b890b16f8c6e9e208426dd7b87d1165f7c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/sit/onlinepay-idc
  newTag: e8ebf4b890b16f8c6e9e208426dd7b87d1165f7c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-onlinepay-idc/sit/onlinepay-internal-idc
  newTag: e8ebf4b890b16f8c6e9e208426dd7b87d1165f7c
