apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: jkopay-api-crossborder-exchange-ing
  annotations:
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://*.jkopay.app,https://localhost:*"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Accept-Language, Content-Type, Authorization"
spec:
  tls:
    - hosts:
        - sit-migrantworkerremit-inner.jkopay.app
      secretName: wildcard-jkopay-app-tls-secret
  rules:
    - host: sit-migrantworkerremit-inner.jkopay.app
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: jkopay-api-crossborder-exchange-svc
                port:
                  number: 8080
