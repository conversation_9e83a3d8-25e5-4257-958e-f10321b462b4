apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: rd1
resources:
- ../../base
- ./ingress.int.external.yaml
patches:
- path: deployment.patch.yaml
- path: deployment.internal.patch.yaml
- path: deployment.preview.patch.yaml
- path: service.patch.yaml
- path: service.internal.patch.yaml
- path: service.int.yaml
- path: service.preview.patch.yaml
- path: ingress.patch.yaml
- path: ingress.internal.patch.yaml
- path: ingress.int.yaml
- path: ingress.preview.patch.yaml
- path: rollout.patch.yaml
- path: prometheus-rules.patch.yml
- path: servicemonitor.patch.yml
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/prod/authpay
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/prod/authpay-internal
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/prod/authpay-preview
  newTag: 99e53f0ca8a1d89983dda706de6f4d1a6f745a9c
