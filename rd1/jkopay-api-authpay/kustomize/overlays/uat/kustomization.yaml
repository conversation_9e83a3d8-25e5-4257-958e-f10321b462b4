apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: rd1
namePrefix: uat-
resources:
- ../../base
patches:
- path: ./deployment.patch.yaml
- path: ./deployment.internal.patch.yaml
- path: ./deployment.preview.patch.yaml
- path: ./service.patch.yaml
- path: ./service.internal.patch.yaml
- path: ./service.int.yaml
- path: ./service.preview.patch.yaml
- path: ./ingress.patch.yaml
- path: ./ingress.internal.patch.yaml
- path: ./ingress.int.yaml
- path: ./ingress.preview.patch.yaml
- path: ./rollout.patch.yaml
- path: prometheus-rules.patch.yml
- path: servicemonitor.patch.yml
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/uat/authpay
  newTag: e8ebf4b890b16f8c6e9e208426dd7b87d1165f7c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/uat/authpay-internal
  newTag: 9daa974be844b49e572a6cba1db7df95e9898b85
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/rd1/jkopay-api-authpay/uat/authpay-preview
  newTag: 9daa974be844b49e572a6cba1db7df95e9898b85
