apiVersion: v1
kind: Service
metadata:
  labels:
    app: foundation-jkopay-batchsystem-api
    environment: sit
  name: sit-foundation-jkopay-batchsystem-api-svc
  namespace: foundation
spec:
  ports:
  - name: http-port
    port: 9090
    protocol: TCP
    targetPort: 9090
  - name: metrics-port
    port: 9091
    protocol: TCP
    targetPort: 9091
  selector:
    app: foundation-jkopay-batchsystem-api
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-f2e-jkopay-batchsystem-operator
  namespace: f2e
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: f2e
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: f2e-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: foundation-jkopay-batchsystem-api
    developer: foundation
    environment: sit
    service: foundation-jkopay-batchsystem-api
  name: sit-foundation-jkopay-batchsystem-api
  namespace: foundation
spec:
  replicas: 1
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-api
  template:
    metadata:
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
        vault.hashicorp.com/agent-inject-secret-env-config: secret/data/sit/foundation/app/jkopay-batchsystem-api
        vault.hashicorp.com/agent-inject-template-env-config: |
          {{- with secret "secret/data/sit/foundation/app/jkopay-batchsystem-api" -}}
          #!/bin/sh
          {{- range $k, $v := .Data.data }}
          export {{ $k }}='{{ $v }}'
          {{- end }}
          exec "$@"
          {{- end }}
        vault.hashicorp.com/role: foundation
      labels:
        app: foundation-jkopay-batchsystem-api
        environment: sit
    spec:
      containers:
      - command:
        - /bin/sh
        - -c
        - ' if [ -f "/vault/secrets/env-config" ]; then source "/vault/secrets/env-config";
          fi; dotnet JKOPay.BatchSystem.Api.dll {{workflow.uid}} {{workflow.name}}
          {{workflow.namespace}} {{inputs.parameters.jobData}}'
        env:
        - name: ASPNETCORE_ENVIRONMENT
          value: sit
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-api:f17e408da77bb876c7f779071e5e30b16573851b
        livenessProbe:
          httpGet:
            path: /healthz
            port: 9090
          initialDelaySeconds: 40
          periodSeconds: 15
        name: jkopay-batchsystem-api
        ports:
        - containerPort: 9090
        readinessProbe:
          httpGet:
            path: /healthz
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 512Mi
          requests:
            cpu: "0.4"
            memory: 128Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: vault-foundation-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-foundation-jkopay-batchsystem-operator
  namespace: foundation
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: foundation
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: foundation-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-rd1-jkopay-batchsystem-operator
  namespace: rd1
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: rd1
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: rd1-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-rd2-jkopay-batchsystem-operator
  namespace: rd2
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: rd2
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: rd2-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-rd3-jkopay-batchsystem-operator
  namespace: rd3
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: rd3
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: rd3-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-rd4-jkopay-batchsystem-operator
  namespace: rd4
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: rd4
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: rd4-batchsystem-sa
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/name: jkopay-batchsystem-operator
    environment: sit
  name: sit-rd5-jkopay-batchsystem-operator
  namespace: rd5
spec:
  replicas: 1
  revisionHistoryLimit: 4
  selector:
    matchLabels:
      app: jkopay-batchsystem-operator
  template:
    metadata:
      labels:
        app: jkopay-batchsystem-operator
        environment: sit
    spec:
      containers:
      - env:
        - name: NETCORE_ENVIRONMENT
          value: sit
        - name: NAMESPACE
          value: rd5
        image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator:ae538f94ee0e005bb1de0770bf933086a828009e
        livenessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 15
          periodSeconds: 20
        name: jkopay-batchsystem-operator
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
        resources:
          limits:
            cpu: "1"
            memory: 256Mi
          requests:
            cpu: "0.2"
            memory: 64Mi
      imagePullSecrets:
      - name: jkopay-operator-garcfg
      serviceAccountName: rd5-batchsystem-sa
---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Schedule job to periodically call scrape-schedule-job-status-job-wt
    workflows.argoproj.io/version: '>= 3.2.0'
  labels:
    app: batchsystem-api
    company: jkopay
    environment: sit
    location: idc
    module: foundation
    namespace: foundation
    serviceRole: foundation-scrape-schedule-job-status-job-sj
    serviceType: job
    team: foundation
  name: sit-foundation-scrape-schedule-job-status-job-sj
  namespace: foundation
spec:
  schedule: '*/5 * * * *'
  workflowSpec:
    arguments:
      parameters:
      - name: jobName
        value: foundation-scrape-schedule-job-status-job-sj
      - name: templateName
        value: sit-foundation-scrape-schedule-job-status-job-wt
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNjcmFwZS1zY2hlZHVsZS1qb2Itc3RhdHVzLWpvYiIgfQ==
      - name: priority
        value: schedule
      - name: team
        value: foundation
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: name
          value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
        - key: status
          value: '{{status}}'
        - key: workflowId
          value: '{{workflow.uid}}'
        - key: scheduledTime
          value: '{{workflow.scheduledTime}}'
        name: cronwf_execute_result_counter
      - gauge:
          realtime: false
          value: '{{workflow.duration}}'
        help: Duration of cron workflow execution
        labels:
        - key: name
          value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
        - key: status
          value: '{{status}}'
        name: cronwf_execute_duration
    workflowTemplateRef:
      name: sit-foundation-batchsystem-main-cron-workflow-template
---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  annotations:
    workflows.argoproj.io/description: |
      Shared CronWorkflow Process
    workflows.argoproj.io/version: '>= 3.2.0'
  labels:
    app: batchsystem-api
    company: jkopay
    environment: sit
    location: gcp
    module: foundation
    namespace: foundation
    serviceRole: foundation-test-job-sj
    serviceType: idc
    team: foundation
  name: sit-foundation-test-job-sj
  namespace: foundation
spec:
  schedule: '*/5 */5 * * *'
  workflowSpec:
    arguments:
      parameters:
      - name: jobName
        value: foundation-test-job-sj
      - name: templateName
        value: sit-foundation-job-wt
      - name: jobData
        value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogInNsYWNram9iIiB9
      - name: priority
        value: schedule
      - name: team
        value: foundation
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: name
          value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
        - key: status
          value: '{{status}}'
        - key: workflowId
          value: '{{workflow.uid}}'
        - key: scheduledTime
          value: '{{workflow.scheduledTime}}'
        name: cronwf_execute_result_counter
      - gauge:
          realtime: false
          value: '{{workflow.duration}}'
        help: Duration of cron workflow execution
        labels:
        - key: name
          value: '{{workflow.labels.workflows.argoproj.io/cron-workflow}}'
        - key: status
          value: '{{status}}'
        name: cronwf_execute_duration
    workflowTemplateRef:
      name: sit-foundation-batchsystem-main-cron-workflow-template
---
apiVersion: argoproj.io/v1alpha1
kind: EventSource
metadata:
  labels:
    app.kubernetes.io/instance: kafka-eventsource
    app.kubernetes.io/name: kafka-eventsource
    app.kubernetes.io/part-of: jkopay-batchsystem
    environment: sit
  name: sit-kafka-eventsource
  namespace: argo
spec:
  eventBusName: default
  kafka:
    priority:
      connectionBackoff:
        duration: 2s
        factor: 2
        jitter: 0.2
        steps: 5
      consumerGroup:
        groupName: found-batchsystem-workflow
        oldest: false
        rebalanceStrategy: range
      jsonBody: false
      limitEventsPerSecond: 1
      partition: "0"
      sasl:
        mechanism: PLAIN
        passwordSecret:
          key: KAFKA_PASSWORD
          name: sit-argo-kafka-user
        userSecret:
          key: KAFKA_USERNAME
          name: sit-argo-kafka-user
      topic: foundation_batchsystem_workflow_sit
      url: ka1.jkopay.app:9093,ka2.jkopay.app:9093,ka3.jkopay.app:9093
      version: 2.5.0
  serviceAccountName: argo
---
apiVersion: argoproj.io/v1alpha1
kind: Sensor
metadata:
  name: sit-kafka-sensor
  namespace: argo
spec:
  dependencies:
  - eventName: priority
    eventSourceName: sit-kafka-eventsource
    name: batchsystem-dep-kafka
  eventBusName: default
  template:
    serviceAccountName: argo
  triggers:
  - template:
      argoWorkflow:
        operation: submit
        parameters:
        - dest: spec.arguments.parameters.0.value
          src:
            dataKey: body
            dependencyName: batchsystem-dep-kafka
        - dest: spec.arguments.parameters.1.value
          src:
            dataKey: headers.jobId
            dependencyName: batchsystem-dep-kafka
        - dest: spec.arguments.parameters.2.value
          src:
            dataKey: headers.templateName
            dependencyName: batchsystem-dep-kafka
        - dest: spec.arguments.parameters.3.value
          src:
            dataKey: headers.priority
            dependencyName: batchsystem-dep-kafka
        - dest: spec.workflowTemplateRef.name
          src:
            dataKey: headers.entry
            dependencyName: batchsystem-dep-kafka
        - dest: spec.ServiceAccountName
          src:
            dataKey: headers.teamAccount
            dependencyName: batchsystem-dep-kafka
        - dest: metadata.namespace
          src:
            dataKey: headers.team
            dependencyName: batchsystem-dep-kafka
        - dest: metadata.name
          src:
            dataKey: headers.name
            dependencyName: batchsystem-dep-kafka
        resource: workflows
        source:
          resource:
            apiVersion: argoproj.io/v1alpha1
            kind: Workflow
            metadata:
              generateName: kafka-workflow-
            spec:
              arguments:
                parameters:
                - name: jobData
                - name: jobId
                - name: templateName
                - name: priority
              entryPoint: main
      name: kafka-workflow-trigger
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-f2e-batchsystem-main-cron-workflow-template
  namespace: f2e
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: f2e-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-f2e-batchsystem-workflow-template
  namespace: f2e
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: f2e-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-foundation-batchsystem-main-cron-workflow-template
  namespace: foundation
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: foundation-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-foundation-batchsystem-workflow-template
  namespace: foundation
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: foundation-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app: batchsystem-api
    company: jkopay
    environment: sit
    location: idc
    module: foundation
    namespace: foundation
    serviceRole: foundation-job-wt
    serviceType: job
    team: foundation
  name: sit-foundation-job-wt
  namespace: foundation
spec:
  arguments:
    parameters:
    - name: jobData
      value: eyAicGFyYW1ldGVyIjoge30sICJqb2JOYW1lIjogImxpbmVqb2IiIH0=
  serviceAccountName: foundation-batchsystem-sa
  templates:
  - container:
      command:
      - /bin/sh
      - -c
      - ' if [ -f "/vault/secrets/env-config" ]; then source "/vault/secrets/env-config";
        fi; dotnet JKOPay.BatchSystem.Job.dll {{workflow.uid}} {{workflow.name}} {{workflow.namespace}}
        {{inputs.parameters.jobData}}'
      env:
      - name: NETCORE_ENVIRONMENT
        value: sit
      - name: IS_BASE64
        value: "true"
      image: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-job:bf179459a1af9310efcaf7f81f238c936ad32262
    inputs:
      parameters:
      - name: jobData
    metadata:
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-inject-command-env-config: cat /vault/secrets/env-config
        vault.hashicorp.com/agent-inject-secret-env-config: secret/data/sit/foundation/app/jkopay-batchsystem-api
        vault.hashicorp.com/agent-inject-template-env-config: |
          {{- with secret "secret/data/sit/foundation/app/jkopay-batchsystem-api" -}}
          #!/bin/sh
          {{- range $k, $v := .Data.data }}
          export {{ $k }}='{{ $v }}'
          {{- end }}
          exec "$@"
          {{- end }}
        vault.hashicorp.com/role: foundation-batchsystem
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: name
          value: '{{workflow.parameters.templateName}}'
        - key: status
          value: '{{status}}'
        name: job_execute_result_counter
      - gauge:
          realtime: false
          value: '{{duration}}'
        help: Duration of workflow execution
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: name
          value: '{{workflow.parameters.templateName}}'
        - key: status
          value: '{{status}}'
        name: job_execute_duration
    name: main
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app: batchsystem-api
    company: jkopay
    environment: sit
    location: idc
    module: foundation
    namespace: foundation
    serviceRole: foundation-scrape-schedule-job-status-job-wt
    serviceType: job
    team: foundation
  name: sit-foundation-scrape-schedule-job-status-job-wt
  namespace: foundation
spec:
  serviceAccountName: foundation-batchsystem-sa
  templates:
  - name: main
    steps:
    - - arguments:
          parameters:
          - name: url
            value: http://sit-foundation-jkopay-batchsystem-api-svc.foundation:9090/api/v1/meter/scheduleJobList
        name: call-api
        template: http-request
  - http:
      method: GET
      successCondition: response.statusCode == 200
      url: '{{inputs.parameters.url}}'
    inputs:
      parameters:
      - name: url
    name: http-request
    outputs:
      parameters:
      - name: response-body
        valueFrom:
          jsonPath: '{$.result}'
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-rd1-batchsystem-main-cron-workflow-template
  namespace: rd1
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd1-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-rd1-batchsystem-workflow-template
  namespace: rd1
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd1-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-rd2-batchsystem-main-cron-workflow-template
  namespace: rd2
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd2-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-rd2-batchsystem-workflow-template
  namespace: rd2
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd2-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-rd3-batchsystem-main-cron-workflow-template
  namespace: rd3
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd3-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-rd3-batchsystem-workflow-template
  namespace: rd3
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd3-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-rd4-batchsystem-main-cron-workflow-template
  namespace: rd4
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd4-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-rd4-batchsystem-workflow-template
  namespace: rd4
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd4-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-main-cron-workflow-template
    environment: sit
  name: sit-rd5-batchsystem-main-cron-workflow-template
  namespace: rd5
spec:
  arguments:
    parameters:
    - name: jobName
      value: default
    - name: templateName
      value: default
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: team
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd5-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobName
            value: '{{workflow.parameters.jobName}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
          - name: templateName
            value: '{{workflow.parameters.templateName}}'
          - name: team
            value: '{{workflow.parameters.team}}'
        name: initialize-job-and-status
        template: initialize-job-and-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - initialize-job-and-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: main
      - arguments:
          parameters:
          - name: jobId
            value: '{{tasks.initialize-job-and-status.outputs.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    metrics:
      prometheus:
      - counter:
          value: "1"
        help: Count of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow.parameters.templateName}}'
        - key: result
          value: '{{status}}'
        - key: crateTimestamp
          value: '{{workflow.creationTimestamp}}'
        - key: finishTimestamp
          value: '{{workflow.status.finishedAt}}'
        name: wf_execute_result_counter
      - gauge:
          realtime: true
          value: '{{workflow.duration}}'
        help: Time of execution by result status
        labels:
        - key: team
          value: '{{workflow.namespace}}'
        - key: workflowName
          value: '{{workflow..parameters.templateName}}'
        name: wf_execution_time
    name: main
  - containerSet:
      containers:
      - args:
        - |
          echo "=== Creating job record with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- API URL: {{workflow.parameters.apiUrl}}"

          # Network diagnostics
          echo ""
          echo "=== Network Diagnostics ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          # Check if it's a k8s service or pod
          if echo "$api_host_clean" | grep -q "\.svc\.cluster\.local$"; then
            echo "Target appears to be a k8s Service: $api_host_clean"
          else
            echo "Target appears to be a Pod or external service: $api_host_clean"
          fi

          # Display network interfaces
          echo "Network interfaces:"
          ip addr show 2>/dev/null || ifconfig 2>/dev/null || echo "Network interface info not available"

          # Display routing info
          echo "Routing information:"
          ip route 2>/dev/null || route -n 2>/dev/null || echo "Routing info not available"

          # Test k8s DNS service availability
          echo "Testing k8s DNS service..."
          if nslookup kubernetes.default.svc.cluster.local >/dev/null 2>&1; then
            echo "✓ k8s DNS service is working"
          else
            echo "✗ k8s DNS service test failed"
          fi

          # Test basic HTTP connectivity
          echo "Testing basic HTTP connectivity to target..."

          # Test health endpoints
          for health_path in "/healthz/liveness"; do
            if wget --spider --timeout=5 --tries=1 "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable"
            elif curl --connect-timeout 5 --max-time 10 --silent --fail "http://$api_host$health_path" >/dev/null 2>&1; then
              echo "✓ Health endpoint ($health_path) is reachable (via curl)"
            else
              echo "✗ Health endpoint ($health_path) is not reachable"
            fi
          done

          echo ""
          echo "=== API Request Details ==="
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/create/schedule"
          payload='{
            "jobName": "{{inputs.parameters.jobName}}",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "workflowTemplateName": "{{inputs.parameters.templateName}}",
            "status": "{{inputs.parameters.status}}",
            "priority": "{{inputs.parameters.priority}}",
            "team": "{{inputs.parameters.team}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making API request..."
          create_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          create_response=$(echo "$create_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$create_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$create_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== API Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $create_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: HTTP request failed with status $http_status" >&2
            echo "Full response: $create_response_raw" >&2
            exit 1
          fi

          # Extract jobId with multiple methods
          echo ""
          echo "=== Extracting JobId ==="

          # Method 1: grep + awk
          jobId=$(echo "$create_response" | grep -o '"jobId": *"[^"]*"' | awk -F '"' '{print $4}')

          # Method 2: sed (fallback)
          if [ -z "$jobId" ]; then
            echo "Method 1 failed, trying method 2..."
            jobId=$(echo "$create_response" | sed -n 's/.*"jobId": *"\([^"]*\)".*/\1/p')
          fi

          # Method 3: jq if available (fallback)
          if [ -z "$jobId" ] && command -v jq >/dev/null 2>&1; then
            echo "Method 2 failed, trying jq..."
            jobId=$(echo "$create_response" | jq -r '.ResultObject.JobId // .resultObject.jobId // .JobId // .jobId // empty' 2>/dev/null)
          fi

          # Method 4: Check common response patterns
          if [ -z "$jobId" ]; then
            echo "Previous methods failed, trying pattern matching..."
            for pattern in '"JobId":"[^"]*"' '"jobId":"[^"]*"' '"ResultObject":{[^}]*"JobId":"[^"]*"'; do
              jobId=$(echo "$create_response" | grep -o "$pattern" | sed 's/.*"\([^"]*\)"/\1/')
              [ -n "$jobId" ] && break
            done
          fi

          if [ -z "$jobId" ]; then
            echo "Error: Job ID not found in the response after trying multiple extraction methods" >&2
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$create_response" | wc -c)" >&2
            echo "- Contains 'jobId': $(echo "$create_response" | grep -c 'jobId')" >&2
            echo "- Contains 'JobId': $(echo "$create_response" | grep -c 'JobId')" >&2
            echo "Full response: $create_response" >&2
            exit 1
          fi

          echo "✓ Successfully extracted jobId: $jobId"

          # Save jobId to shared volume for next container
          echo "$jobId" > /tmp/shared/jobId
          echo ""
          echo "=== Job Creation Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Job record creation completed successfully"
        command:
        - sh
        - -c
        image: alpine/curl
        name: create-job
      - args:
        - |
          echo "=== Status Update with enhanced diagnostics ==="

          # Display environment information
          echo "Environment Information:"
          echo "- Hostname: $(hostname)"
          echo "- Namespace: {{workflow.namespace}}"
          echo "- Current time: $(date -Iseconds)"
          echo "- Target Status: {{inputs.parameters.status}}"

          echo ""
          echo "=== Reading JobId from shared volume ==="
          if [ ! -f "/tmp/shared/jobId" ]; then
            echo "Error: jobId file not found in shared volume" >&2
            echo "Shared volume contents:" >&2
            ls -la /tmp/shared/ 2>&1 >&2 || echo "Cannot list shared volume" >&2
            exit 1
          fi

          jobId=$(cat /tmp/shared/jobId)
          echo "✓ Successfully read jobId: $jobId"

          # Basic network diagnostics for status update
          echo ""
          echo "=== Network Diagnostics for Status Update ==="
          api_host="{{workflow.parameters.apiUrl}}"
          api_host_clean=$(echo $api_host | cut -d':' -f1)

          echo "API Host: $api_host_clean"

          # Quick connectivity test
          if ping -c 1 -W 2 "$api_host_clean" >/dev/null 2>&1; then
            echo "✓ Host is pingable"
          else
            echo "✗ Host ping failed (may be normal for some configurations)"
          fi

          # Test API endpoint availability
          api_url="http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status"
          if curl --connect-timeout 5 --max-time 10 --silent --fail --head "$api_url" >/dev/null 2>&1; then
            echo "✓ Status update endpoint is reachable"
          else
            echo "✗ Status update endpoint connectivity test failed"
          fi

          echo ""
          echo "=== Status Update Request Details ==="
          payload='{
            "jobId": "'$jobId'",
            "workflowId": "{{workflow.uid}}",
            "workflowName": "{{workflow.name}}",
            "Status": "{{inputs.parameters.status}}",
            "Priority": "{{inputs.parameters.priority}}"
          }'

          echo "API URL: $api_url"
          echo "Request payload: $payload"
          echo ""

          # Make API call with enhanced logging
          echo "Making status update request..."
          update_response_raw=$(curl --silent --show-error --write-out "HTTPSTATUS:%{http_code};TOTAL_TIME:%{time_total}" \
          --request POST --url "$api_url" \
          --header 'Accept: */*' \
          --header 'Content-Type: application/json' \
          --header 'User-Agent: batchsystem' \
          --data "$payload" 2>&1)

          # Parse response
          update_response=$(echo "$update_response_raw" | sed 's/HTTPSTATUS:.*$//')
          http_status=$(echo "$update_response_raw" | grep -o 'HTTPSTATUS:[0-9]*' | cut -d':' -f2)
          total_time=$(echo "$update_response_raw" | grep -o 'TOTAL_TIME:[0-9.]*' | cut -d':' -f2)

          echo "=== Status Update Response Details ==="
          echo "HTTP Status: $http_status"
          echo "Response Time: ${total_time}s"
          echo "Response Body: $update_response"

          # Check HTTP status
          if [ "$http_status" != "200" ]; then
            echo "Error: Status update HTTP request failed with status $http_status" >&2
            echo "Full response: $update_response_raw" >&2
            exit 1
          fi

          # Check if update was successful (response body contains "0001")
          echo ""
          echo "=== Validating Response Content ==="
          if echo "$update_response" | grep -q "0001"; then
            echo "✓ Response contains success indicator (0001)"
            success_status="SUCCESS"
          else
            echo "✗ Response does not contain success indicator (0001)"
            echo "Response analysis:" >&2
            echo "- Length: $(echo "$update_response" | wc -c)" >&2
            echo "- Contains '0001': $(echo "$update_response" | grep -c '0001')" >&2
            echo "- Contains 'success': $(echo "$update_response" | grep -ic 'success')" >&2
            echo "- Contains 'error': $(echo "$update_response" | grep -ic 'error')" >&2
            echo "Full response: $update_response" >&2
            exit 1
          fi

          # Save jobId for downstream workflow tasks
          echo "$jobId" > /tmp/jobId

          echo ""
          echo "=== Status Update Summary ==="
          echo "✓ HTTP Status: $http_status"
          echo "✓ Response Time: ${total_time}s"
          echo "✓ JobId: $jobId"
          echo "✓ Target Status: {{inputs.parameters.status}}"
          echo "✓ Validation: $success_status"
          echo "✓ Status update completed successfully"
        command:
        - sh
        - -c
        dependencies:
        - create-job
        image: alpine/curl
        name: main
      volumeMounts:
      - mountPath: /tmp/shared
        name: shared
    inputs:
      parameters:
      - name: jobName
      - name: templateName
      - name: status
      - name: priority
      - name: team
    name: initialize-job-and-status
    outputs:
      parameters:
      - name: jobId
        valueFrom:
          path: /tmp/jobId
    volumes:
    - emptyDir: {}
      name: shared
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  labels:
    app.kubernetes.io/name: batchsystem-workflow-template
    environment: sit
  name: sit-rd5-batchsystem-workflow-template
  namespace: rd5
spec:
  arguments:
    parameters:
    - name: jobId
      value: default
    - name: templateName
      value: default
    - name: entryPoint
      value: main
    - name: jobData
      value: default
    - name: priority
      value: default
    - name: apiUrl
      value: sit-foundation-jkopay-batchsystem-api-svc.foundation:9090
  entrypoint: main
  imagePullSecrets:
  - name: jkopay-operator-garcfg
  serviceAccountName: rd5-batchsystem-sa
  templates:
  - dag:
      tasks:
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowStarted
          - name: priority
            value: '{{workflow.parameters.priority}}'
        name: started-status
        template: update-status
      - arguments:
          parameters:
          - name: jobData
            value: '{{workflow.parameters.jobData}}'
        dependencies:
        - started-status
        name: team-job
        templateRef:
          name: '{{workflow.parameters.templateName}}'
          template: '{{workflow.parameters.entryPoint}}'
      - arguments:
          parameters:
          - name: jobId
            value: '{{workflow.parameters.jobId}}'
          - name: status
            value: WorkflowComplete
          - name: priority
            value: '{{workflow.parameters.priority}}'
        dependencies:
        - team-job
        name: complete-workflow
        template: update-status
    name: main
  - http:
      body: |
        {
          "jobId": "{{inputs.parameters.jobId}}",
          "workflowId": "{{workflow.uid}}",
          "workflowName": "{{workflow.name}}",
          "Status": "{{inputs.parameters.status}}",
          "Priority": "{{inputs.parameters.priority}}"
        }
      headers:
      - name: Content-Type
        value: application/json
      method: POST
      successCondition: response.body contains "0001"
      timeoutSeconds: 10
      url: http://{{workflow.parameters.apiUrl}}/api/v1/job/update/status
    inputs:
      parameters:
      - name: jobId
      - name: status
      - name: priority
    name: update-status
---
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    environment: sit
    release: sit-sre-kube-prometheus-stack-idc
  name: sit-batchsystem-operator-rules
  namespace: foundation
spec:
  groups:
  - name: argo-workflow-failed.rules
    rules:
    - alert: ArgoWorkflowFailed
      annotations:
        description: |
          <@U05SW9BGX7H>
          [IDC-SIT] 在 `{{ $labels.team }}` namespace 中檢測到失敗的工作流，名稱為 : `{{ $labels.name }}`
        summary: '[IDC-SIT] Argo Workflow Failed Alert (Argo Workflow 執行失敗)'
      expr: |
        (
            count by (name, namespace, team)(
            (max_over_time(argo_workflows_cronwf_execute_result_counter{status="Failed"}[3m] offset 30s))
            )
        ) > 0
      labels:
        environment: sit
        moduelteam: '{{ $labels.team }}'
        module: argo-workflow
        service: sit-argo-workflow
        severity: critical
        team: foundation
  - name: batchsystem-job.rules
    rules:
    - alert: DisableScheduleJobDetected
      annotations:
        description: |
          <@U05SW9BGX7H>
          [SIT] BatchSystem Job `{{ $labels.jobName}}` disable detected (發現 BatchSystem Job 關閉)
        summary: |
          [IDC-SIT] BatchSystem Job disable detected
      expr: |
        (
          count by (jobName, namespace)(
            (schedule_job_list == 0) and
            (max_over_time(schedule_job_list[10m] offset 2m) == 1)
          )
        ) > 0
      for: 10s
      labels:
        environment: sit
        jobName: '{{ $labels.jobName }}'
        modulalteam: '{{ $labels.namespace }}'
        service: sit-batchsystem-job
        severity: warning
        team: foundation
  - interval: 2m
    name: batchysystem-operator.rules
    rules:
    - alert: BatchSystemOperatorServiceDown
      annotations:
        dashboard_uid: aeo2ufw1vfvggd
        description: |
          <@U05SW9BGX7H>
          [IDC-SIT] BatchSystem Operator service `{{ $labels.team }}-{{ $labels.service }}` is down. (服務停止中，心跳數沒有增加)
          Service Information:
          - team: {{ $labels.team }}
          - service: {{ $labels.service }}
          - value: {{ $value }}
        panel_id: "11"
        summary: '[IDC-SIT] BatchSystem Operator service `{{ $labels.team }}-{{ $labels.service
          }}` is down.'
      expr: increase(batchsystem_operator_heartbeat_unit_total[2m]) < 18
      for: 2m
      labels:
        environment: sit
        moduelteam: '{{ $labels.team }}'
        module: batchsystem-operator
        service: sit-batchsystem-operator
        severity: critical
        team: foundation
    - alert: BatchSystemOperatorServiceDrop
      annotations:
        dashboard_uid: aeo2ufw1vfvggd
        description: |
          <@U05SW9BGX7H>
          [IDC-SIT] BatchSystem Operator Service Drop Alert (服務異常，心跳數消失)
          Drop Service:
          - Name: {{ $labels.service }}
          - Namespace: {{ $labels.team }}
        panel_id: "65"
        summary: '[IDC-SIT] BatchSystem Operator service is drop.'
      expr: |
        count by (service, team)(
            (count by (service, team)(increase(batchsystem_operator_heartbeat_unit_total[2m] offset 12m)) > 0)
            unless
            (count by (service, team)(increase(batchsystem_operator_heartbeat_unit_total[2m])) > 0)
        ) > 0
      for: 5m
      labels:
        environment: sit
        service: sit-batchsystem-operator
        severity: critical
        team: foundation
---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    app: foundation-batchsystem-api-monitor
    release: sit-sre-kube-prometheus-stack-idc
  name: sit-foundation-batchsystem-api-monitor
  namespace: monitoring
spec:
  endpoints:
  - path: /metrics
    port: http-port
  namespaceSelector:
    matchNames:
    - foundation
  selector:
    matchLabels:
      app: foundation-jkopay-batchsystem-api
      environment: sit
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    kubernetes.io/ingress.class: foundation
  labels:
    environment: sit
  name: sit-foundation-jkopay-batchsystem-ing
  namespace: foundation
spec:
  ingressClassName: foundation-nginx
  rules:
  - host: sit-batchsystem.jkopay.app
    http:
      paths:
      - backend:
          service:
            name: sit-foundation-jkopay-batchsystem-api-svc
            port:
              number: 9090
        path: /
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - sit-batchsystem.jkopay.app
    secretName: wildcard-jkopay-app-tls-secret
---
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  labels:
    app: jkopay-batchsystem-api
    component: kafka
    environment: sit
    type: vault
  name: sit-jkopay-batchsystem-api-secret
  namespace: argo
spec:
  destination:
    create: true
    name: sit-argo-kafka-user
  mount: secret
  path: sit/foundation/app/jkopay-batchsystem-api
  refreshAfter: 1h
  type: kv-v2
  vaultAuthRef: sit-vault-foundation-argo-event-auth
