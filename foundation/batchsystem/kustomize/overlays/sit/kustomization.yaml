apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namePrefix: sit-
resources:
- ../../base
- ../../../utils/overlays/sit
- ./batchsystem-ext-name-ing.yaml
configurations:
- kustomconfig.yaml
patches:
- path: ./jobs/foundation-job-wt-patch.yaml
- path: ./jobs/foundation-test-job-sj-patch.yaml
- path: ./jobs/foundation-scrape-schedule-job-status-job-wt-patch.yaml
- path: ./jobs/foundation-scrape-schedulejob-status-job-sj-patch.yaml
- path: ./batchsystem-api-deployment-patch.yaml
- path: ./batchsystem-api-svc-patch.yaml
- path: ./kafka-vault-secret-patch.yaml
- path: ./kafka-eventsource-patch.yaml
- path: ./servicemonitor-patch.yaml
- path: ./prometheus-rules.patch.yaml
- patch: |
    - op: replace
      path: /spec/kafka/priority/sasl/passwordSecret/name
      value: sit-argo-kafka-user
  target:
    kind: EventSource
    name: kafka-eventsource
- patch: |
    - op: replace
      path: /spec/kafka/priority/sasl/userSecret/name
      value: sit-argo-kafka-user
  target:
    kind: EventSource
    name: kafka-eventsource
- patch: |
    - op: replace
      path: /spec/dependencies/0/eventSourceName
      value: sit-kafka-eventsource
  target:
    kind: Sensor
    name: kafka-sensor
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-api
  newTag: f17e408da77bb876c7f779071e5e30b16573851b
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-job
  newTag: bf179459a1af9310efcaf7f81f238c936ad32262
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/sit/jkopay-batchsystem-operator
  newTag: ae538f94ee0e005bb1de0770bf933086a828009e
