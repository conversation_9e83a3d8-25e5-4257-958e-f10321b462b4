apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
resources:
- ../../base
- ../../../utils/overlays/prod
- ./batchsystem-ext-name-ing.yaml
configurations:
- kustomconfig.yaml

patches:
- path: ./jobs/foundation-job-wt-patch.yaml
- path: ./jobs/foundation-test-job-sj-patch.yaml
- path: ./jobs/foundation-scrape-schedule-job-status-job-wt-patch.yaml
- path: ./jobs/foundation-scrape-schedulejob-status-job-sj-patch.yaml
- path: ./batchsystem-api-deployment-patch.yaml
- path: ./batchsystem-api-svc-patch.yaml
- path: ./kafka-vault-secret-patch.yaml
- path: ./kafka-eventsource-patch.yaml
- path: ./servicemonitor-patch.yaml
- path: ./prometheus-rules.patch.yaml
- patch: |
    - op: replace
      path: /spec/kafka/priority/sasl/passwordSecret/name
      value: argo-kafka-user
  target:
    kind: EventSource
    name: kafka-eventsource
- patch: |
    - op: replace
      path: /spec/kafka/priority/sasl/userSecret/name
      value: argo-kafka-user
  target:
    kind: EventSource
    name: kafka-eventsource
- patch: |
    - op: replace
      path: /spec/dependencies/0/eventSourceName
      value: kafka-eventsource
  target:
    kind: Sensor
    name: kafka-sensor
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/prod/jkopay-batchsystem-api
  newTag: 1b04da031f6fa2087d65c708de7bb80e826483b7
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/prod/jkopay-batchsystem-job
  newTag: b22765c8f7c5e0fe9d707df6f21518d6adca3906
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/foundation/batchsystem/prod/jkopay-batchsystem-operator
  newTag: 31fcb42ae5105cd91cbc67667996282ef866898f
