apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization
namespace: f2e
namePrefix: sit-
resources:
- ../../base
patches:
- path: ./deployment.app.patch.yaml
- path: ./deployment.cms.patch.yaml
- path: ./service.app.patch.yaml
- path: ./service.cms.patch.yaml
- path: ./ingress.app.patch.yaml
- path: ./ingress.cms.patch.yaml
- path: ./servicemonitor.patch.yaml
- path: ./staticsecret.patch.yaml
images:
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/f2e/jkopay-mofang-lerna/sit/["app",
    "cms"]
  newTag: 9ca2bc3e6742afa31250e17c4687f20613d25bd4
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/f2e/jkopay-mofang-lerna/sit/app
  newTag: 4a265967523131325e8927c3f90adf6603ae2a2c
- name: asia-east1-docker.pkg.dev/jkopay-operator/app-docker-repository/f2e/jkopay-mofang-lerna/sit/cms
  newTag: 4a265967523131325e8927c3f90adf6603ae2a2c
