apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: jkopay-rd2-prometheus-rules
  namespace: rd2
  labels:
    release: kube-prometheus-stack
spec:
  groups:
    - name: jkopay-rd2-prometheus.rules
      rules:
        - alert: CobrandCoinBalanceAnomaly
          annotations:
            summary: '台新街口幣餘額異常'
            description: |
              "台新街口幣餘額出現異常，異常筆數: {{ $value }} 筆（CoBrandID = 1 且 EvenExpire <> 0）"
          expr: |
            cobrand_coin_balance_anomaly_total > 0
          for: 5m
          labels:
            severity: critical
            service: jkopay-rd2-prometheus-rules
            team: rd2
